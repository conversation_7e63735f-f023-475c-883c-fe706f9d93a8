extends Node
class_name FontLoader

# Gotické fonty pre Prekliate Dedičstvo
# Načítava Google Fonts s fallback možnosťami

# Font paths
const FONTS_DIR = "res://fonts/"

# Google Fonts URLs
const GOOGLE_FONTS_URLS = {
	"cinzel": "https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600&display=swap",
	"cormorant": "https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,400;1,400&display=swap",
	"crimson": "https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;1,400&display=swap",
	"montserrat": "https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500&display=swap",
	"uncial": "https://fonts.googleapis.com/css2?family=Uncial+Antiqua&display=swap"
}

# Fallback fonts
const FALLBACK_FONTS = {
	"gothic_serif": ["Times New Roman", "Book Antiqua", "serif"],
	"readable_serif": ["Georgia", "Times New Roman", "serif"],
	"clean_sans": ["Arial", "Helvetica", "sans-serif"],
	"decorative": ["Papyrus", "Bradley Hand", "fantasy"]
}

# Font configurations
const FONT_CONFIGS = {
	"chapter_title": {
		"primary": "Cinzel",
		"fallback": "gothic_serif",
		"size": 28,
		"color": Color(0.831, 0.686, 0.216), # #D4AF37 zlatá
		"weight": "600",
		"shadow": true
	},
	"character_dialogue": {
		"primary": "Cormorant Garamond",
		"fallback": "readable_serif",
		"size": 18,
		"color": Color(0.961, 0.961, 0.863), # #F5F5DC krémová
		"weight": "400",
		"shadow": false
	},
	"narrator_text": {
		"primary": "Crimson Text",
		"fallback": "readable_serif",
		"size": 16,
		"color": Color(0.878, 0.878, 0.878), # #E0E0E0 svetlá sivá
		"weight": "400",
		"italic": true,
		"shadow": false
	},
	"ui_elements": {
		"primary": "Montserrat",
		"fallback": "clean_sans",
		"size": 16,
		"color": Color.WHITE,
		"weight": "500",
		"shadow": false
	},
	"puzzle_text": {
		"primary": "Uncial Antiqua",
		"fallback": "decorative",
		"size": 20,
		"color": Color(0.545, 0.271, 0.075), # #8B4513 červenohnedá
		"weight": "400",
		"shadow": true
	}
}

# Responzívne škálovanie
const MOBILE_SCALE = {
	"tablet": 0.9,  # 768px a menej
	"mobile": 0.8   # 480px a menej
}

static func get_font_config(font_type: String) -> Dictionary:
	"""Získa konfiguráciu fontu podľa typu"""
	if FONT_CONFIGS.has(font_type):
		return FONT_CONFIGS[font_type]
	else:
		return FONT_CONFIGS["ui_elements"]  # default

static func get_scaled_font_size(base_size: int, screen_width: int) -> int:
	"""Vypočíta škálovanú veľkosť fontu pre responzívnosť"""
	if screen_width <= 480:
		return int(base_size * MOBILE_SCALE["mobile"])
	elif screen_width <= 768:
		return int(base_size * MOBILE_SCALE["tablet"])
	else:
		return base_size

static func create_font_variation(font_type: String, custom_size: int = -1) -> SystemFont:
	"""Vytvorí font s danou konfiguráciou"""
	var config = get_font_config(font_type)

	# Pre Godot 4 - použijeme SystemFont s fallback
	var font = SystemFont.new()

	# Nastavenie font family s fallback
	var font_names = PackedStringArray()
	font_names.append(config.get("primary", "Arial"))

	var fallback_key = config.get("fallback", "clean_sans")
	if FALLBACK_FONTS.has(fallback_key):
		for fallback_font in FALLBACK_FONTS[fallback_key]:
			font_names.append(fallback_font)

	font.font_names = font_names

	# Nastavenie štýlu
	if config.has("weight"):
		var weight_str = config.get("weight", "400")
		if weight_str == "600":
			font.font_weight = 600
		elif weight_str == "500":
			font.font_weight = 500
		else:
			font.font_weight = 400

	if config.get("italic", false):
		font.font_style = 1  # FONT_STYLE_ITALIC

	return font

static func apply_font_style(label: Control, font_type: String, custom_size: int = -1):
	"""Aplikuje font štýl na Label alebo RichTextLabel"""
	var config = get_font_config(font_type)
	var font = create_font_variation(font_type, custom_size)
	
	# Aplikovanie fontu
	if label is Label:
		label.add_theme_font_override("font", font)
		label.add_theme_font_size_override("font_size", config.get("size", 16))
		label.add_theme_color_override("font_color", config.get("color", Color.WHITE))
		
		# Outline a shadow
		if config.get("shadow", false):
			label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 0.8))
			label.add_theme_constant_override("shadow_offset_x", 2)
			label.add_theme_constant_override("shadow_offset_y", 2)
		
	elif label is RichTextLabel:
		label.add_theme_font_override("normal_font", font)
		label.add_theme_font_size_override("normal_font_size", config.get("size", 16))
		label.add_theme_color_override("default_color", config.get("color", Color.WHITE))

# Utility funkcie pre rôzne typy textov
static func apply_chapter_title_font(label: Control, custom_size: int = -1):
	apply_font_style(label, "chapter_title", custom_size)

static func apply_character_dialogue_font(label: Control, custom_size: int = -1):
	apply_font_style(label, "character_dialogue", custom_size)

static func apply_narrator_font(label: Control, custom_size: int = -1):
	apply_font_style(label, "narrator_text", custom_size)

static func apply_ui_font(label: Control, custom_size: int = -1):
	apply_font_style(label, "ui_elements", custom_size)

static func apply_puzzle_font(label: Control, custom_size: int = -1):
	apply_font_style(label, "puzzle_text", custom_size)
