extends Node

# Jednoduchý test fontov - spustí sa pri načítaní

func _ready():
	print("=== TESTOVANIE FONTOV ===")
	test_font_creation()

func test_font_creation():
	print("Testovanie vytvárania fontov...")
	
	# Test FontLoader
	var font_loader = preload("res://scripts/font_loader.gd")
	
	# Test vytvorenia rôznych typov fontov
	var chapter_font = font_loader.create_font_variation("chapter_title")
	var dialogue_font = font_loader.create_font_variation("character_dialogue")
	var narrator_font = font_loader.create_font_variation("narrator_text")
	var ui_font = font_loader.create_font_variation("ui_elements")
	var puzzle_font = font_loader.create_font_variation("puzzle_text")
	
	print("✅ Chapter font vytvorený: ", chapter_font != null)
	print("✅ Dialogue font vytvorený: ", dialogue_font != null)
	print("✅ Narrator font vytvorený: ", narrator_font != null)
	print("✅ UI font vytvorený: ", ui_font != null)
	print("✅ Puzzle font vytvorený: ", puzzle_font != null)
	
	# Test font names
	if chapter_font:
		print("Chapter font names: ", chapter_font.font_names)
	if dialogue_font:
		print("Dialogue font names: ", dialogue_font.font_names)
	
	print("=== TEST DOKONČENÝ ===")
	
	# Ukončiť po 3 sekundách
	await get_tree().create_timer(3.0).timeout
	print("Test ukončený.")
	get_tree().quit()
