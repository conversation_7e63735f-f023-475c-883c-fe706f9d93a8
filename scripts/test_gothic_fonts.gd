extends Control

# Test scéna pre gotické fonty
# Zobrazuje všetky typy fontov v jednej scéne

@onready var container: VBoxContainer = $VBoxContainer

func _ready():
	create_font_samples()

func create_font_samples():
	print("=== TESTOVANIE GOTICKÝCH FONTOV ===")
	
	# Načítanie font settings
	var chapter_settings = preload("res://themes/ChapterTitleSettings.tres")
	var dialogue_settings = preload("res://themes/DialogueSettings.tres")
	var narrator_settings = preload("res://themes/NarratorSettings.tres")
	var puzzle_settings = preload("res://themes/PuzzleSettings.tres")
	
	# Vytvorenie ukážok
	create_sample_label("KAPITOLA 1: PRÍCHOD DO ZÁMKU", chapter_settings, "Cinzel - Nadpisy kapitol")
	create_sample_label("Viktor: 'Vitajte v zámku, pán doktor.'", dialogue_settings, "Cormoran<PERSON>mond - Dialógy postav")
	create_sample_label("Rozprávač: Vstupujete do temnej chodby...", narrator_settings, "Crimson Text - Rozprávačský text")
	create_sample_label("Začať hru", null, "Montserrat - UI elementy (z témy)")
	create_sample_label("𝔄𝔫𝔠𝔦𝔢𝔫𝔱 ℑ𝔫𝔰𝔠𝔯𝔦𝔭𝔱𝔦𝔬𝔫", puzzle_settings, "Uncial Antiqua - Hádanky a záhady")
	
	# Responzívne testovanie
	test_responsive_scaling()

func create_sample_label(text: String, settings: LabelSettings, description: String):
	# Popis fontu
	var desc_label = Label.new()
	desc_label.text = description
	desc_label.add_theme_color_override("font_color", Color.YELLOW)
	container.add_child(desc_label)
	
	# Ukážka textu
	var sample_label = Label.new()
	sample_label.text = text
	if settings:
		sample_label.label_settings = settings
	sample_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	container.add_child(sample_label)
	
	# Oddeľovač
	var spacer = Control.new()
	spacer.custom_minimum_size = Vector2(0, 20)
	container.add_child(spacer)

func test_responsive_scaling():
	print("Testovanie responzívneho škálovania...")
	
	var screen_size = get_viewport().get_visible_rect().size
	print("Veľkosť obrazovky: ", screen_size)
	
	if screen_size.x <= 480:
		print("Mobilné zariadenie - aplikujem 80% škálovanie")
	elif screen_size.x <= 768:
		print("Tablet - aplikujem 90% škálovanie")
	else:
		print("Desktop - normálne škálovanie")

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		get_tree().quit()
