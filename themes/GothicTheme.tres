[gd_resource type="Theme" load_steps=14 format=3 uid="uid://2g01d2kieu86"]

[ext_resource type="Texture2D" uid="uid://b2d5arlfnaage" path="res://assets/Buttons/Buttons_1.png" id="1_button_normal"]
[ext_resource type="Texture2D" uid="uid://ck1icbesh5uvy" path="res://assets/Buttons/Button_Selected.png" id="2_button_hover"]
[ext_resource type="Texture2D" uid="uid://dugfxtgj8dwft" path="res://assets/Buttons/Buttons_3.png" id="3_button_pressed"]
[ext_resource type="Texture2D" uid="uid://d00mi5x66kt6g" path="res://assets/Scalable screen/Scalable_2.png" id="4_panel"]
[ext_resource type="FontFile" uid="uid://cinzel_gothic_font" path="res://fonts/CinzelFont.tres" id="5_cinzel_font"]
[ext_resource type="FontFile" uid="uid://cormorant_dialogue_font" path="res://fonts/CormorantFont.tres" id="6_cormorant_font"]
[ext_resource type="FontFile" uid="uid://crimson_narrator_font" path="res://fonts/CrimsonFont.tres" id="7_crimson_font"]
[ext_resource type="FontFile" uid="uid://montserrat_ui_font" path="res://fonts/MontserratFont.tres" id="8_montserrat_font"]
[ext_resource type="FontFile" uid="uid://uncial_puzzle_font" path="res://fonts/UncialFont.tres" id="9_uncial_font"]

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_normal"]
texture = ExtResource("1_button_normal")
texture_margin_left = 8.0
texture_margin_top = 8.0
texture_margin_right = 8.0
texture_margin_bottom = 8.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_hover"]
texture = ExtResource("2_button_hover")
texture_margin_left = 8.0
texture_margin_top = 8.0
texture_margin_right = 8.0
texture_margin_bottom = 8.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_button_pressed"]
texture = ExtResource("3_button_pressed")
texture_margin_left = 8.0
texture_margin_top = 8.0
texture_margin_right = 8.0
texture_margin_bottom = 8.0

[sub_resource type="StyleBoxTexture" id="StyleBoxTexture_panel"]
texture = ExtResource("4_panel")
texture_margin_left = 16.0
texture_margin_top = 16.0
texture_margin_right = 16.0
texture_margin_bottom = 16.0

[resource]
default_font_size = 18
default_font = ExtResource("8_montserrat_font")
Button/colors/font_color = Color(0.9, 0.85, 0.7, 1)
Button/colors/font_disabled_color = Color(0.5, 0.5, 0.5, 0.7)
Button/colors/font_hover_color = Color(1, 0.95, 0.8, 1)
Button/colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
Button/colors/font_pressed_color = Color(0.8, 0.75, 0.6, 1)
Button/constants/outline_size = 2
Button/fonts/font = ExtResource("8_montserrat_font")
Button/font_sizes/font_size = 18
Button/styles/disabled = SubResource("StyleBoxTexture_button_normal")
Button/styles/hover = SubResource("StyleBoxTexture_button_hover")
Button/styles/normal = SubResource("StyleBoxTexture_button_normal")
Button/styles/pressed = SubResource("StyleBoxTexture_button_pressed")
Label/colors/font_color = Color(0.9, 0.85, 0.7, 1)
Label/colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
Label/colors/font_shadow_color = Color(0.2, 0.1, 0.05, 0.8)
Label/constants/outline_size = 1
Label/constants/shadow_offset_x = 2
Label/constants/shadow_offset_y = 2
Label/fonts/font = ExtResource("6_cormorant_font")
Label/font_sizes/font_size = 18
LineEdit/colors/font_color = Color(0.9, 0.85, 0.7, 1)
LineEdit/colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
LineEdit/colors/font_selected_color = Color(1, 1, 1, 1)
LineEdit/colors/selection_color = Color(0.6, 0.4, 0.2, 0.5)
LineEdit/constants/outline_size = 1
LineEdit/fonts/font = ExtResource("8_montserrat_font")
LineEdit/font_sizes/font_size = 16
PanelContainer/styles/panel = SubResource("StyleBoxTexture_panel")
RichTextLabel/colors/default_color = Color(0.9, 0.85, 0.7, 1)
RichTextLabel/colors/font_outline_color = Color(0.2, 0.1, 0.05, 1)
RichTextLabel/colors/font_shadow_color = Color(0.2, 0.1, 0.05, 0.8)
RichTextLabel/constants/outline_size = 1
RichTextLabel/constants/shadow_offset_x = 1
RichTextLabel/constants/shadow_offset_y = 1
RichTextLabel/fonts/normal_font = ExtResource("6_cormorant_font")
RichTextLabel/font_sizes/normal_font_size = 16
